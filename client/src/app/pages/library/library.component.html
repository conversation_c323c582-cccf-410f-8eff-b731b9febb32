<div class="library-page">
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div *ngIf="data" class="wrapper_line custom_">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{data.title}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
    </div>
    <div *ngIf="data" class="flex relative justify-center tabs_w">
      <div class="library-tabs">
        <div class="library-tab" [ngClass]="{'is-active': tab === item }" *ngFor="let item of tabs"
          (click)="changeTab(item)">
          {{item}}
        </div>
      </div>
    </div>

    <div class="wrapper_line custom_l">
      <div class="library" *ngIf="data">
        <ng-container *ngIf="tab !== 'Читать'; else bookTextTab">
          <div class="library_header_action flex items-center justify-between gap-3">
            <div class="flex items-center action_header_wrapper">
              <button class="flex items-center icons_w lik_hov btn_like"
              [ngClass]="{'is-liked': isLiked(data.id)}"
              (click)="like(data.id)">
              <div class="icon-wrap like_w ">
                <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--text-color)" />
                </svg>
              </div>
              <span class="ml-2 likes-count">{{likesCount}}</span>
            </button>

            <div class="flex items-center cursor-pointer icons_w fav_hov btn-favourite"
              [ngClass]="{'in-favourites': isInFavourites(data.id)}"
              (click)="favorite(data.id)">
              <div class="icon-wrap star_w">
                <app-favorites-icon></app-favorites-icon>
              </div>
              <span class="ml-2">избранное</span>
            </div>
            <div class="flex items-center cursor-pointer icons_w shr_hov share_icon" (click)="share(data)">
              <div class="icon-wrap share_w">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                    fill="var(--font-color1)" />
                </svg>
              </div>
              <span class="ml-2">поделиться</span>
            </div>
              <ng-container *ngIf="tab === 'Аудиокнига' && hasAudio">
                <ng-container *ngTemplateOutlet="allTime; context: {durationMinutes: durationMinutes}"></ng-container>
              </ng-container>
            </div>
            <div class="desktop-add-queue-btn" *ngIf="tab === 'Аудиокнига'">
              <ng-container *ngTemplateOutlet="buttonAddQueue"></ng-container>
            </div>
          </div>
          <div class="library-page">
            <div class="book_top">
              <div class="book-img_btn-buy-section">
                <div class="img_book flex justify-center items-center">
                  @if(isMobile) {
                    @if(data.image_mobile) {
                      <img [src]="data.image_mobile" alt='{{data.title}}' width='300' height='200'>
                    } @else {
                      <img [src]="data.image" alt='{{data.title}}'>
                    }
                  } @else {
                    <img [src]="data.image" alt='{{data.title}}'>
                  }
                </div>
                @if(checkPurchase()) {
                  <ng-container *ngTemplateOutlet="buttonBuyBook"></ng-container>
                } 
                
                @if (checkSubscription()) {
                  <ng-container *ngTemplateOutlet="buttonSubscribe"></ng-container>
                }
              </div>
              <ng-container [ngSwitch]="tab">
                <!-- book about tab -->
                <div *ngSwitchCase="'О книге'" class="book_about">
                  <p class="book_text">
                    <ng-container *ngIf="!showFullAnnotation && data.annotation.length > 630">
                      {{data.annotation.substring(0, 630)}}...
                    </ng-container>
                    <ng-container *ngIf="showFullAnnotation || data.annotation.length <= 630">
                      {{data.annotation}}
                    </ng-container>
                  </p>
                  <p *ngIf="data.annotation.length > 630" class="book_text underline cursor-pointer" (click)="toggleAnnotationDisplay()">
                    {{showFullAnnotation ? 'Свернуть' : 'Читать полностью'}}
                  </p>
                  <div class="author_t">{{data.author}}</div>
                  <p class="book_text scnd">Философия учения, {{data.pages}} страницы.</p>
                  <div class="book_chips desktop-book_chips" *ngIf="data.tags.length">
                    @for(tag of data.tags; track $index) {
                      <div class="book_chip" (click)="navigateToTaggedLibrary(tag.id)">{{tag.name}}</div>
                    }
                  </div>
                </div>


                <!-- audio tab -->

                <div *ngSwitchCase="'Аудиокнига'" class="audio-section">
                  <div class="mobile-add-queue-btn">
                    <ng-container *ngTemplateOutlet="buttonAddQueue"></ng-container>
                  </div>
                  <div class="library-audio__list">
                    <div class="library-audio__list-inner">
                      <ng-container *ngIf="data.audio.length">
                        <div class="library-audio flex gap-4 items-center"
                        *ngFor="let audio of data.audio;let i = index" [ngClass]="{'active-audio': audioBookIndex === i}"
                        (click)="play([{url: audio.url, chapter: audio.chapter, time: audio.time, index: i}]); audioBookIndex = i">
                          <div class="library-play cursor-pointer">
                            <!-- TO DO add logic for change play icon-->

                            <!-- <ng-container *ngIf="!playState; pauseIcon">
                              <ng-container *ngTemplateOutlet="playIcon"></ng-container>
                            </ng-container> -->

                            <ng-container *ngTemplateOutlet="playIcon"></ng-container> <!-- When the logic to change the play icon is added, delete this line -->
                          </div>
                          <div class="audio-name">{{audio.chapter}}</div>
                          <div class="audio-time">{{secondsToHMS(audio.duration)}}</div>
                        </div>
                      </ng-container>
                    </div>
                  </div>
                  <div class="audio-section-footer flex items-center justify-between">
                    <div class="reader-name">
                      Читает {{data.reader}}
                    </div>
                  </div>
                  <div class="book_chips desktop-book_chips" *ngIf="data.tags.length">
                    @for(tag of data.tags; track $index) {
                      <div class="book_chip" (click)="navigateToTaggedLibrary(tag.id)">{{tag.name}}</div>
                    }
                  </div>
                </div>
              </ng-container>
            </div>
            <div class="book_chips mobile-book_chips" *ngIf="data.tags.length">
              @for(tag of data.tags; track $index) {
                <div class="book_chip" (click)="navigateToTaggedLibrary(tag.id)">{{tag.name}}</div>
              }
            </div>
            <div class="lotus_divider">
              <img src="assets/images/icons/lotus_divider.svg" alt="divider">
            </div>

            <div class="related_books_section">
              <div class="related_books_title">
                Похожие книги:
                <div class="mobile-related-books-buttons">
                  <div class="prev-button cursor-pointer">
                    <img src="assets/images/icons/arrow-in-circle.svg" alt="prev" (click)="moveLeft()">
                  </div>
                  <div class="next-button cursor-pointer">
                    <img src="assets/images/icons/arrow-in-circle.svg" alt="next" (click)="moveRight()">
                  </div>
                </div>
              </div>
              <div id="carousel"
                class="related_books_container"
                [ngClass]="{'show-last-book': relatedBooksShowLast}"
                >
                <div (click)="openBook(book.code, $event)" class="related_book cursor-pointer" *ngFor="let book of similar">
                  <div class="img_book flex justify-center items-center">
                    <img [src]="book.image" alt='{{book.title}}'>
                  </div>
                  <div class="related_book_name">
                    {{book.title}}
                  </div>
                  <div class="related_book_author">
                    {{book.author}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>

      </div>
    </div>
  </div>
</div>

<ng-template #bookTextTab>

  <div class="library-tab-content">
    <div class="flex flex-col book_text_wrapper">
      <div class="flex justify-between book-text-header">
      <div class="flex full-screen-btn gap-2 items-center hidden-mobile" (click)="openFullscreenDialog()">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M23.9969 0.518898C23.9969 0.643849 23.9969 0.7688 23.9969 0.893751C23.3659 2.0433 22.891 3.25533 22.4787 4.49859C21.7103 6.79144 21.2417 9.14677 21.1917 11.5708C21.1355 14.2198 21.6041 16.7938 22.4287 19.3053C22.8536 20.5985 23.3471 21.8668 24.0031 23.0601C24.0031 23.185 24.0031 23.31 24.0031 23.4349C23.8907 23.6661 23.722 23.841 23.4846 23.9472C23.3596 23.9472 23.2347 23.9472 23.1097 23.9472C23.0972 23.9347 23.091 23.9222 23.0785 23.916C21.8415 23.2912 20.5545 22.7664 19.23 22.3416C17.0183 21.6294 14.7567 21.1983 12.4326 21.1483C9.56501 21.0859 6.80359 21.6294 4.10464 22.5665C2.99883 22.9539 1.918 23.385 0.893401 23.9472C0.768449 23.9472 0.643498 23.9472 0.518547 23.9472C0.287388 23.8348 0.112456 23.6661 0 23.4349C0 23.31 0 23.185 0 23.0601C0.631003 21.9043 1.11207 20.6798 1.53065 19.4365C2.29285 17.1499 2.76142 14.8008 2.80515 12.3893C2.86138 9.74029 2.39281 7.1663 1.56814 4.65478C1.1558 3.35529 0.662241 2.09328 0.00624756 0.893751C0.00624756 0.7688 0.00624756 0.643849 0.00624756 0.518898C0.312378 0.000350755 0.631003 -0.118353 1.09332 0.112807C3.41741 1.27485 5.86646 2.06204 8.42795 2.49937C10.9895 2.94295 13.5385 2.89922 16.0875 2.41191C18.474 1.95584 20.7606 1.19988 22.9348 0.100312C23.3596 -0.112105 23.7345 0.0190934 23.9531 0.443927C23.9594 0.468917 23.9781 0.493908 23.9969 0.518898ZM2.19914 21.7606C8.74658 19.0741 15.269 19.0741 21.8415 21.8106C20.542 18.5931 19.7798 15.3568 19.7798 11.9707C19.7798 8.58449 20.5482 5.34201 21.829 2.16201C18.624 3.449 15.3815 4.2112 11.9828 4.2112C8.59664 4.20496 5.35416 3.43651 2.14916 2.15576C4.89184 8.72819 4.88559 15.2319 2.19914 21.7606Z" fill="var(--book_about)"/>
          <path d="M7.71958 6.71009C8.22564 7.20989 8.70045 7.6847 9.16902 8.15952C9.28772 8.27822 9.40642 8.38443 9.51263 8.50938C9.78128 8.80926 9.77503 9.2341 9.50639 9.4965C9.23774 9.76514 8.80666 9.77139 8.51302 9.49025C7.97573 8.9717 7.45094 8.43441 6.9199 7.90962C6.86367 7.85339 6.80119 7.79716 6.73247 7.73468C6.71373 7.75967 6.70123 7.77217 6.70123 7.77842C6.65125 8.18451 6.40135 8.44691 6.03899 8.46565C5.67039 8.48439 5.37675 8.22824 5.29553 7.8284C5.18308 7.27861 5.07687 6.72883 4.95816 6.18529C4.92068 6.01036 4.8707 5.84167 4.83321 5.6605C4.7395 5.16694 5.17058 4.73586 5.66414 4.83582C6.40135 4.98576 7.14481 5.1482 7.88202 5.30439C8.25687 5.3856 8.49428 5.68549 8.47554 6.03535C8.46304 6.40395 8.20065 6.64761 7.71958 6.71009Z" fill="var(--book_about)"/>
          <path d="M6.72877 16.2311C6.80374 16.1624 6.86622 16.1062 6.92244 16.0499C7.44724 15.5251 7.96579 15.0003 8.49683 14.4818C8.74673 14.2381 9.07785 14.1882 9.35274 14.3381C9.60889 14.4818 9.76508 14.7692 9.69636 15.0691C9.65887 15.2252 9.56516 15.3877 9.4527 15.5064C8.9529 16.0312 8.4281 16.5372 7.9158 17.0495C7.85958 17.1058 7.8096 17.162 7.73463 17.237C7.78461 17.2495 7.8096 17.2557 7.83459 17.2557C8.22194 17.3119 8.47809 17.5743 8.48433 17.9304C8.49683 18.2928 8.24693 18.5739 7.85333 18.6552C7.2973 18.7676 6.73502 18.8801 6.17898 18.9925C6.0103 19.03 5.84786 19.08 5.67918 19.1112C5.17313 19.2112 4.74205 18.7739 4.84825 18.2741C4.9982 17.5431 5.15439 16.8121 5.31057 16.0812C5.39179 15.7063 5.68543 15.4689 6.03529 15.4814C6.3914 15.4939 6.64755 15.75 6.70378 16.1436C6.71627 16.1624 6.72252 16.1811 6.72877 16.2311Z" fill="var(--book_about)"/>
          <path d="M16.2664 17.2441C16.1977 17.1691 16.1477 17.1129 16.0977 17.0566C15.6042 16.5631 15.1044 16.0633 14.6108 15.5697C14.2297 15.1886 14.186 14.77 14.4859 14.4639C14.792 14.1578 15.2168 14.1953 15.5917 14.5701C16.1415 15.1199 16.6913 15.6697 17.2535 16.2382C17.2723 16.1945 17.2848 16.182 17.291 16.1632C17.341 15.7571 17.6034 15.501 17.972 15.4885C18.3344 15.476 18.6155 15.7321 18.6967 16.1195C18.8217 16.7443 18.9529 17.369 19.0841 17.9938C19.1028 18.0937 19.1341 18.1874 19.159 18.2874C19.259 18.781 18.8279 19.2183 18.3406 19.1183C17.5909 18.9684 16.8474 18.8059 16.0977 18.6435C15.7354 18.5623 15.5042 18.2687 15.523 17.925C15.5355 17.5752 15.7916 17.3128 16.1602 17.2628C16.179 17.2566 16.2102 17.2503 16.2664 17.2441Z" fill="var(--book_about)"/>
          <path d="M16.3199 6.66065C15.8388 6.59818 15.5764 6.34203 15.564 5.96717C15.5577 5.58607 15.8263 5.32368 16.2699 5.23621C16.9509 5.10501 17.6256 4.95507 18.3066 4.80513C18.8877 4.68018 19.3125 5.10501 19.1875 5.67979C19.0376 6.37326 18.8877 7.0605 18.7502 7.75397C18.669 8.16631 18.3941 8.42246 18.0317 8.41622C17.6506 8.40997 17.3882 8.14132 17.332 7.66651C17.257 7.73523 17.1946 7.78521 17.1446 7.84144C16.6323 8.35374 16.12 8.86604 15.6077 9.37834C15.2703 9.70946 14.8392 9.73445 14.5518 9.44706C14.2582 9.15343 14.2832 8.72235 14.6143 8.39123C15.1266 7.87893 15.6389 7.36663 16.1512 6.85433C16.195 6.80435 16.2449 6.74187 16.3199 6.66065Z" fill="var(--book_about)"/>
        </svg>
        <span>
          Открыть на весь экран
        </span>
      </div>
        <a [href]="data.link" *ngIf="data?.link && (data.price && data.isPurchased || libraryService.hasAccess())" download>Скачать книгу</a>
        <div class="flex items-center book-content-section">
          <ng-container *ngTemplateOutlet="readingTime; context: {readingMinutes: chapterReadingTime}"></ng-container>
          <button (click)="contents = !contents" class="flex items-center cursor-pointer icons_w relative contents-btn"
                  appClickOutside (clickOutside)="contents = false">
            <div class="icon-wrap content_w">
              <svg class="contents-btn-icon" width="22" height="18" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="4" width="18" height="2" rx="1" fill="var(--font-color1)" />
                <rect width="3" height="2" rx="1" fill="var(--font-color1)" />
                <rect x="4" y="8" width="18" height="2" rx="1" fill="var(--font-color1)" />
                <rect y="8" width="3" height="2" rx="1" fill="var(--font-color1)" />
                <rect x="4" y="16" width="18" height="2" rx="1" fill="var(--font-color1)" />
                <rect y="16" width="3" height="2" rx="1" fill="var(--font-color1)" />
              </svg>
            </div>
            <span class="ml-2 text-color default_ btn-label">содержание</span>
            @if (contents) {
            <div class="library-content-context contents_">
              <p class="conts_title">Содержание</p>
              <div class="conts_list" *ngIf="chapterTitles && chapterTitles.length">
                  @for(chapterTitle of chapterTitles; track $index) {
                  <p class="conts_item" [ngClass]="{'active': $index === chapter}" (click)="getChapter($index)">{{chapterTitle}}</p>
                  }
              </div>
            </div>
            }
          </button>
        </div>
      </div>
      <text-interaction
        *ngIf="chapterContent"
        [contentId]="data.id"
        [contentHtml]="chapterContent"
        [type]="'library'"
        [chapter]="chapter"
        class="book_text_section"
      ></text-interaction>

      <!-- Loading indicator -->
      <app-loading-indicator
        *ngIf="!chapterContent"
        text="Загрузка главы..."
        class="book_text_section">
      </app-loading-indicator>
      <div class="flex justify-center gap-3 book-text-nav book_text_section" *ngIf="chapterContent">
        <div class="prev_btn" [ngClass]="{'disabled': chapter===0}" (click)="prev()">
          Предыдущая
        </div>
        <div class="page-indicator">
          Глава {{chapter + 1}} из {{chapterCount}}
        </div>
        <div class="next_btn" [ngClass]="{'disabled': chapter === chapterCount - 1}" (click)="next()">
          Cледующая
        </div>
      </div>
    </div>
    <div class="scroll_top" [ngClass]="{'is-visible': showScrollTop}" (click)="scrollToTop()"></div>
  </div>
</ng-template>

<ng-template #allTime let-durationMinutes="durationMinutes">
  <div class="all-time flex gap-2 items-center" *ngIf="durationMinutes > 0">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.25 0C11.75 0 12.25 0 12.75 0C12.8125 0.0125261 12.8687 0.0313152 12.9312 0.0375783C13.6812 0.169102 14.4625 0.212944 15.1812 0.438413C19.9188 1.8977 22.8062 5.06681 23.8125 9.93319C23.9 10.3653 23.9375 10.81 24 11.2484C24 11.7495 24 12.2505 24 12.7516C23.9875 12.8142 23.9688 12.8706 23.9625 12.9332C23.8375 14.5804 23.4187 16.1524 22.6125 17.5992C20.3438 21.6451 16.8875 23.8121 12.25 23.9937C9.75625 24.0939 7.44375 23.3987 5.36875 22.0021C2.54375 20.0856 0.8125 17.4489 0.1875 14.0919C0.10625 13.6472 0.0625 13.1962 0 12.7516C0 12.2505 0 11.7495 0 11.2484C0.0125 11.1795 0.03125 11.1106 0.0375 11.0418C0.11875 10.4969 0.16875 9.93946 0.28125 9.40084C1.25 4.74739 5.21875 0.951983 9.90625 0.187891C10.3562 0.112735 10.8 0.0626305 11.25 0ZM1.925 12.9395C2.375 18.0125 6.55 21.7328 11.0625 22.0647C11.0625 21.8017 11.0625 21.5386 11.0625 21.2693C11.0688 20.6931 11.4563 20.286 12 20.286C12.5437 20.286 12.9312 20.6931 12.9375 21.2693C12.9375 21.5324 12.9375 21.7954 12.9375 22.0647C17.7875 21.714 21.75 17.5741 22.0562 12.9395C21.9375 12.9395 21.825 12.9395 21.7062 12.9395C20.7125 12.9395 20.2812 12.6514 20.2812 11.9937C20.2875 11.3486 20.7125 11.0605 21.6938 11.0605C21.8188 11.0605 21.9375 11.0605 22.0625 11.0605C21.65 5.92484 17.2875 2.19207 12.9375 1.94781C12.9375 2.20459 12.9375 2.46138 12.9375 2.71816C12.9312 3.30689 12.5437 3.72025 11.9875 3.71399C11.45 3.70772 11.0625 3.30063 11.0625 2.71816C11.0625 2.45511 11.0625 2.19207 11.0625 1.92902C5.75 2.39248 2.15625 6.90188 1.95 11.0668C2.19375 11.0668 2.43125 11.0668 2.675 11.0668C3.29375 11.0668 3.70625 11.4426 3.7125 12C3.7125 12.5637 3.3 12.9395 2.6625 12.9457C2.425 12.9395 2.18125 12.9395 1.925 12.9395Z" fill="var(--font-color1)"/>
      <path d="M11.0627 9.23808C11.0627 8.34247 11.0627 7.44059 11.0627 6.54497C11.0627 5.95625 11.4565 5.53662 12.0002 5.53662C12.544 5.53662 12.9377 5.95625 12.9377 6.55123C12.9377 8.15457 12.944 9.75165 12.9315 11.355C12.9315 11.5429 12.9815 11.6744 13.1127 11.8059C14.1752 12.8581 15.2252 13.9166 16.2815 14.9688C16.5065 15.1942 16.6502 15.4573 16.619 15.783C16.5815 16.165 16.3815 16.4406 16.019 16.5784C15.6377 16.7224 15.294 16.6347 15.0065 16.3466C14.3377 15.689 13.6815 15.0251 13.019 14.3613C12.494 13.8352 11.9752 13.3091 11.444 12.7892C11.1815 12.5324 11.0565 12.2443 11.0627 11.8748C11.069 10.9855 11.0627 10.1086 11.0627 9.23808Z" fill="var(--font-color1)"/>
    </svg>
    <div style="color: var(--font-color1);">{{durationMinutes}} минут</div>
  </div>
</ng-template>

<ng-template #readingTime let-readingMinutes="readingMinutes">
  <div class="reading-time flex gap-2 items-center" *ngIf="readingMinutes > 0">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.25 0C11.75 0 12.25 0 12.75 0C12.8125 0.0125261 12.8687 0.0313152 12.9312 0.0375783C13.6812 0.169102 14.4625 0.212944 15.1812 0.438413C19.9188 1.8977 22.8062 5.06681 23.8125 9.93319C23.9 10.3653 23.9375 10.81 24 11.2484C24 11.7495 24 12.2505 24 12.7516C23.9875 12.8142 23.9688 12.8706 23.9625 12.9332C23.8375 14.5804 23.4187 16.1524 22.6125 17.5992C20.3438 21.6451 16.8875 23.8121 12.25 23.9937C9.75625 24.0939 7.44375 23.3987 5.36875 22.0021C2.54375 20.0856 0.8125 17.4489 0.1875 14.0919C0.10625 13.6472 0.0625 13.1962 0 12.7516C0 12.2505 0 11.7495 0 11.2484C0.0125 11.1795 0.03125 11.1106 0.0375 11.0418C0.11875 10.4969 0.16875 9.93946 0.28125 9.40084C1.25 4.74739 5.21875 0.951983 9.90625 0.187891C10.3562 0.112735 10.8 0.0626305 11.25 0ZM1.925 12.9395C2.375 18.0125 6.55 21.7328 11.0625 22.0647C11.0625 21.8017 11.0625 21.5386 11.0625 21.2693C11.0688 20.6931 11.4563 20.286 12 20.286C12.5437 20.286 12.9312 20.6931 12.9375 21.2693C12.9375 21.5324 12.9375 21.7954 12.9375 22.0647C17.7875 21.714 21.75 17.5741 22.0562 12.9395C21.9375 12.9395 21.825 12.9395 21.7062 12.9395C20.7125 12.9395 20.2812 12.6514 20.2812 11.9937C20.2875 11.3486 20.7125 11.0605 21.6938 11.0605C21.8188 11.0605 21.9375 11.0605 22.0625 11.0605C21.65 5.92484 17.2875 2.19207 12.9375 1.94781C12.9375 2.20459 12.9375 2.46138 12.9375 2.71816C12.9312 3.30689 12.5437 3.72025 11.9875 3.71399C11.45 3.70772 11.0625 3.30063 11.0625 2.71816C11.0625 2.45511 11.0625 2.19207 11.0625 1.92902C5.75 2.39248 2.15625 6.90188 1.95 11.0668C2.19375 11.0668 2.43125 11.0668 2.675 11.0668C3.29375 11.0668 3.70625 11.4426 3.7125 12C3.7125 12.5637 3.3 12.9395 2.6625 12.9457C2.425 12.9395 2.18125 12.9395 1.925 12.9395Z" fill="var(--font-color1)"/>
      <path d="M11.0627 9.23808C11.0627 8.34247 11.0627 7.44059 11.0627 6.54497C11.0627 5.95625 11.4565 5.53662 12.0002 5.53662C12.544 5.53662 12.9377 5.95625 12.9377 6.55123C12.9377 8.15457 12.944 9.75165 12.9315 11.355C12.9315 11.5429 12.9815 11.6744 13.1127 11.8059C14.1752 12.8581 15.2252 13.9166 16.2815 14.9688C16.5065 15.1942 16.6502 15.4573 16.619 15.783C16.5815 16.165 16.3815 16.4406 16.019 16.5784C15.6377 16.7224 15.294 16.6347 15.0065 16.3466C14.3377 15.689 13.6815 15.0251 13.019 14.3613C12.494 13.8352 11.9752 13.3091 11.444 12.7892C11.1815 12.5324 11.0565 12.2443 11.0627 11.8748C11.069 10.9855 11.0627 10.1086 11.0627 9.23808Z" fill="var(--font-color1)"/>
    </svg>
    <div style="color: var(--font-color1);">{{readingMinutes}} мин. чтения</div>
  </div>
</ng-template>

<ng-template #buttonBuyBook>
  <div class="btn_book" (click)="openPurchaseModal()">
    Получить доступ
  </div>
</ng-template>

<ng-template #buttonSubscribe>
  <div class="btn_book" (click)="router.navigate(['/ru/profile/subscriptions'])">
    Оформить подписку
  </div>
</ng-template>


<ng-template #buttonAddQueue>
  <button class="add-to-queue-btn" (click)="playList(data.audio)">Добавить в очередь</button>
</ng-template>

<ng-template #playIcon>
  <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.6541 6.36495C12.1153 6.6358 12.1153 7.3642 11.6541 7.63505L0.960271 13.9145C0.525241 14.1699 3.49471e-07 13.8225 3.73213e-07 13.2794L9.22176e-07 0.720594C9.45918e-07 0.177449 0.525242 -0.169903 0.960272 0.0855478L11.6541 6.36495Z" fill="#532E00"/>
  </svg>
</ng-template>

<ng-template #pauseIcon>
  <svg width="9" height="14" viewBox="0 0 9 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="5.625" width="3.375" height="14" fill="#532E00"/>
    <rect width="3.375" height="14" fill="#532E00"/>
  </svg>
</ng-template>




<!-- Add this at the end of the file, outside other containers -->
<dialog #fullscreenDialog class="fullscreen-book-dialog {{fullscreenBookTheme + ' ' + fullscreenBookFont}}" [ngClass]="{'fullscreen-book-dialog-active': isFullscreenBookDialogActive}">
  <div class="dialog-header">
    <div class="settings-container relative mr-[90px]">
      <div class="settings-button cursor-pointer" (click)="showFullscreenOptions=!showFullscreenOptions">
        Aa
      </div>
      <div class="settings-options" *ngIf="showFullscreenOptions">
        <div class="color-themes-options flex gap-[32px]">
          <div class="theme-option light-option" (click)="fullscreenBookTheme='light'; saveFullscreenSettings()" [ngClass]="{'active-theme': fullscreenBookTheme ==='light'}">
            Светлая
          </div>
          <div class="theme-option dark-option" (click)="fullscreenBookTheme='dark'; saveFullscreenSettings()" [ngClass]="{'active-theme': fullscreenBookTheme ==='dark'}">
            Темная
          </div>
          <!-- <div class="theme-option sepia-option" (click)="fullscreenBookTheme='sepia'; saveFullscreenSettings()" [ngClass]="{'active-theme': fullscreenBookTheme ==='sepia'}">
            Сепия
          </div> -->
        </div>
        <div class="fonts-options gap-[16px]">
          <div class="font-option prata-option" [ngClass]="{'active-font': fullscreenBookFont==='Prata'}" (click)="fullscreenBookFont='Prata'; saveFullscreenSettings()">Prata</div>
          <div class="font-option open-sans-option" [ngClass]="{'active-font': fullscreenBookFont==='Open-Sans'}" (click)="fullscreenBookFont='Open-Sans'; saveFullscreenSettings()">Open Sans</div>
          <div class="font-option montserrat-option" [ngClass]="{'active-font': fullscreenBookFont==='Montserrat'}" (click)="fullscreenBookFont='Montserrat'; saveFullscreenSettings()">Montserrat</div>
        </div>
        <div class="font-size-slider-container">
          <div class="slider-wrapper">
            <div class="slider-track">
              <div class="slider-track-active" [style.width]="((fullscreenBookFontSize - 12) / 18) * 100 + '%'"></div>
              <input
              type="range"
              min="12"
              max="30"
              step="1"
              [(ngModel)]="fullscreenBookFontSize"
              (input)="applyFullscreenFontSize()"
              class="font-size-slider">
            </div>
          </div>
          <div class="scale-box flex justify-between">
            <div class="font-size-min">A</div>
            <div class="font-size-medium">A</div>
            <div class="font-size-max">A</div>
          </div>
        </div>
        <div class="text-width-options gap-[16px]">
          <div class="width-option narrow-option" [ngClass]="{'active-width': fullscreenBookTextWidth==='narrow'}" (click)="fullscreenBookTextWidth='narrow'">Узкая колонка</div>
          <div class="width-option medium-option" [ngClass]="{'active-width': fullscreenBookTextWidth==='medium'}" (click)="fullscreenBookTextWidth='medium'">Средняя колонка</div>
          <div class="width-option full-option" [ngClass]="{'active-width': fullscreenBookTextWidth==='full'}" (click)="fullscreenBookTextWidth='full'">Во весь экран</div>
        </div>
      </div>
    </div>
    <div class="dialog-title">{{data?.title}}</div>
    <div class="header-actions flex gap-[16px] items-center">
      <div class="flex items-center book-content-section">
        <div (click)="fullscreenContents = !fullscreenContents" class="flex items-center cursor-pointer icons_w relative contents-btn"
             appClickOutside (clickOutside)="fullscreenContents = false">
          <div class="icon-wrap content_w">
            <svg class="contents-btn-icon" width="22" height="18" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="4" width="18" height="2" rx="1" fill="var(--font-color1)" />
              <rect width="3" height="2" rx="1" fill="var(--font-color1)" />
              <rect x="4" y="8" width="18" height="2" rx="1" fill="var(--font-color1)" />
              <rect y="8" width="3" height="2" rx="1" fill="var(--font-color1)" />
              <rect x="4" y="16" width="18" height="2" rx="1" fill="var(--font-color1)" />
              <rect y="16" width="3" height="2" rx="1" fill="var(--font-color1)" />
            </svg>
          </div>
          <span class="ml-2 text-color default_ btn-label">содержание</span>
          @if (fullscreenContents) {
          <div class="library-content-context contents_">
            <p class="conts_title">Содержание</p>
            <div class="conts_list" *ngIf="chapterTitles && chapterTitles.length">
                @for(chapterTitle of chapterTitles; track $index) {
                <p class="conts_item" [ngClass]="{'active': $index === chapter}" (click)="goToChapterFullscreen($index)">{{chapterTitle}}</p>
                }
            </div>
          </div>
          }
        </div>
      </div>
      <button class="close-button" (click)="closeFullscreenDialog()">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
  </div>

  <div class="book-pages-container">
    <div class="book-page left-page"
         [ngStyle]="getFullscreenFontSizeStyles()"
         [style.--fullscreen-font-family]="fullscreenBookFont === 'Open-Sans' ? 'Open Sans' : fullscreenBookFont">

      <div class="text-content-wrapper" [ngClass]="{'active-theme': fullscreenBookTheme ==='dark'}" [ngStyle]="getFullscreenTextWidthStyles()">
        <text-interaction
          *ngIf="chapterContent"
          [contentId]="data.id"
          [contentHtml]="chapterContent"
          [type]="'library'"
          [chapter]="chapter"
          class="book_text_section fullscreen-text"
          [ngStyle]="getFullscreenFontSizeStyles()"
          [style.fontFamily]="fullscreenBookFont === 'Open-Sans' ? 'Open Sans' : fullscreenBookFont"
        ></text-interaction>
      </div>



      <!-- Loading indicator for fullscreen -->
      <app-loading-indicator
        *ngIf="!chapterContent"
        text="Загрузка главы..."
        [fullscreen]="true">
      </app-loading-indicator>
    </div>
  </div>

  <div class="book-navigation" *ngIf="chapterContent">
    <button class="nav-button prev-button" [ngClass]="{'disabled': chapter === 0}" (click)="prevFullscreen()">
      <span class="btn-label">
        Предыдущая
      </span>
    </button>
    <div class="page-indicator">
      Глава {{chapter + 1}} из {{chapterCount}}
    </div>
    <button class="nav-button next-button" [ngClass]="{'disabled': chapter === chapterCount - 1}" (click)="nextFullscreen()">
      <span class="btn-label">
        Следующая
      </span>
    </button>
  </div>


</dialog>

<!-- Book Purchase Modal -->
<app-book-purchase-modal
  [bookData]="data"
  [isVisible]="showPurchaseModal"
  (close)="closePurchaseModal()"
  (purchaseComplete)="onPurchaseComplete()">
</app-book-purchase-modal>

